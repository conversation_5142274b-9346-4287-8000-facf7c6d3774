import { useState, useRef, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  SendIcon,
  PaperclipIcon,
  CodeIcon,
  LinkIcon,
  ChevronDownIcon,
  ArrowUpIcon,
} from "lucide-react";
import ChainSelector from "@/components/ChainSelector";
import WalletAccountSelector from "@/components/WalletAccountSelector";
import { useActiveWallet, useActiveWalletChain } from "thirdweb/react";
import { supportedChains, getChainById } from "@/lib/chainConfig";

interface MessageInputProps {
  onSendMessage: (message: string) => void;
  isLoading?: boolean;
  compact?: boolean;
}

const MessageInput = ({
  onSendMessage,
  isLoading = false,
  compact = false,
}: MessageInputProps) => {
  const [message, setMessage] = useState("");
  const [showChainSelector, setShowChainSelector] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const wallet = useActiveWallet();
  const activeChain = useActiveWalletChain();

  // Get current chain from wallet or default to Ethereum
  const currentChainId = activeChain?.id ? activeChain.id.toString() : "1";
  const currentChain = getChainById(currentChainId) || supportedChains[0];
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Auto-resize textarea
  useEffect(() => {
    if (textareaRef.current && !compact) {
      textareaRef.current.style.height = "auto";
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  }, [message, compact]);

  const handleSend = () => {
    if (message.trim() && !isLoading) {
      onSendMessage(message);
      setMessage("");

      // Reset textarea height
      if (textareaRef.current) {
        textareaRef.current.style.height = "auto";
      }
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  const toggleChainSelector = () => {
    setShowChainSelector((prev) => !prev);
  };

  if (compact) {
    return (
      <div className="relative">
        <Input
          type="text"
          placeholder={wallet ? "Ask Nebula..." : "Connect wallet to chat"}
          className="w-full bg-muted theme-input py-3 pl-4 pr-10 text-sm"
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          onKeyDown={handleKeyDown}
          disabled={isLoading || !wallet}
        />
        <Button
          className="absolute right-3 top-1/2 transform -translate-y-1/2 text-primary bg-transparent hover:bg-transparent p-0"
          onClick={handleSend}
          disabled={isLoading || !message.trim() || !wallet}
        >
          <SendIcon className="h-4 w-4" />
        </Button>
        <div className="absolute top-full mt-2 right-0">
          <div className="bg-background p-1 rounded-md shadow-md border border-border">
            <Button
              variant="ghost"
              size="sm"
              className="p-2 text-muted-foreground hover:text-primary"
              onClick={toggleChainSelector}
              title="Select Blockchain"
            >
              <LinkIcon className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="p-2 text-muted-foreground hover:text-primary"
              title="Upload File"
            >
              <PaperclipIcon className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="relative max-w-4xl mx-auto">
      {/* Container that looks like a single textarea */}
      <div
        className={`relative bg-muted/30 theme-input rounded-md border outline-none ${
          isFocused ? "ring-2 ring-primary" : ""
        }`}
      >
        {/* Actual textarea for text input */}
        <Textarea
          ref={textareaRef}
          rows={1}
          placeholder={
            wallet ? "Ask Nebula" : "Connect your wallet to start chatting"
          }
          className="w-full bg-transparent border-0 py-3 pl-4 pr-16 text-sm resize-none min-h-[60px] max-h-[120px] overflow-y-auto outline-none focus:outline-none focus:ring-0 focus:border-0"
          style={{
            scrollbarWidth: "thin",
            scrollbarColor: "rgba(255, 255, 255, 0.1) transparent",
            outline: "none !important",
            border: "none !important",
            boxShadow: "none !important",
          }}
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          onKeyDown={handleKeyDown}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          disabled={isLoading || !wallet}
        />

        {/* Bottom section for wallet and chain components */}
        <div className="px-4 pb-3 pt-3 flex items-center justify-between min-h-[40px] relative z-10">
          <div className="flex items-center gap-2 flex-1 overflow-visible">
            <div className="flex-shrink-0 relative z-20">
              <WalletAccountSelector />
            </div>
            {wallet && (
              <div className="relative flex-shrink-0 z-20">
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-xs flex items-center gap-2 text-muted-foreground hover:text-foreground px-3 py-1.5 h-auto rounded-lg bg-muted/20 border border-border/30 hover:bg-primary/10 hover:border-primary/40 hover:shadow-[0_4px_12px_hsl(var(--primary)/0.2)] hover:-translate-y-px transition-all duration-200 focus:outline-none"
                  onClick={toggleChainSelector}
                >
                  <div
                    className={`w-2 h-2 rounded-full`}
                    style={{ backgroundColor: currentChain.color }}
                  ></div>
                  <span className="whitespace-nowrap">{currentChain.name}</span>
                  <ChevronDownIcon className="h-3 w-3" />
                </Button>

                {showChainSelector && (
                  <ChainSelector onClose={() => setShowChainSelector(false)} />
                )}
              </div>
            )}
          </div>

          {/* Send button moved to bottom right */}
          <div className="flex-shrink-0">
            <Button
              size="icon"
              className="nebula-icon-bg text-white h-8 w-8 theme-button focus:outline-none"
              onClick={handleSend}
              disabled={isLoading || !message.trim() || !wallet}
            >
              <ArrowUpIcon className="h-3.5 w-3.5" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MessageInput;
