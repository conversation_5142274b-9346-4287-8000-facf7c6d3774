import { useState, useEffect } from "react";
import {
  ConnectButton,
  useActiveAccount,
  useActiveWallet,
} from "thirdweb/react";
import { createThirdwebClient } from "thirdweb";
import { createWallet } from "thirdweb/wallets";
import { ethereum, polygon, sepolia, polygonAmoy } from "thirdweb/chains";
import { toast } from "@/hooks/use-toast";

// Create the Thirdweb client
const client = createThirdwebClient({
  clientId: import.meta.env.VITE_THIRDWEB_CLIENT_ID as string,
});

// Define supported wallets
const wallets = [
  createWallet("io.metamask"),
  createWallet("com.coinbase.wallet"),
  createWallet("walletConnect"),
];

// Define supported chains (including testnet chains as per user preference)
const chains = [ethereum, polygon, sepolia, polygonAmoy];

// Wallet icon component using thirdweb's wallet image fetching
const WalletIcon = ({
  walletId,
  size = "24",
}: {
  walletId: string;
  size?: string;
}) => {
  const [iconUrl, setIconUrl] = useState<string>("");

  useEffect(() => {
    const fetchWalletIcon = async () => {
      try {
        // Import the wallet info function dynamically
        const { getWalletInfo } = await import("thirdweb/wallets");
        const iconUrl = await getWalletInfo(walletId as any, true);
        setIconUrl(iconUrl);
      } catch (error) {
        console.error("Failed to fetch wallet icon:", error);
        // Fallback to a generic wallet icon
        setIconUrl("");
      }
    };

    fetchWalletIcon();
  }, [walletId]);

  if (!iconUrl) {
    // Fallback to a simple wallet icon while loading or if failed
    return (
      <div
        className="flex items-center justify-center bg-gray-700 rounded-full text-white text-sm"
        style={{ width: size + "px", height: size + "px" }}
      >
        👛
      </div>
    );
  }

  return (
    <img
      src={iconUrl}
      alt={`${walletId} icon`}
      className="rounded-full"
      style={{ width: size + "px", height: size + "px" }}
      onError={() => setIconUrl("")} // Reset to fallback on error
    />
  );
};

// Helper function to get wallet name
const getWalletName = (walletId: string) => {
  switch (walletId) {
    case "io.metamask":
      return "MetaMask";
    case "com.coinbase.wallet":
      return "Coinbase Wallet";
    case "walletConnect":
      return "WalletConnect";
    default:
      return "Wallet";
  }
};

// Helper function to truncate address
const truncateAddress = (address: string) => {
  return `${address.slice(0, 6)}...${address.slice(-4)}`;
};

// Custom wallet details component
const CustomWalletDetails = () => {
  const account = useActiveAccount();
  const wallet = useActiveWallet();

  if (!account?.address || !wallet) return null;

  const walletName = getWalletName(wallet.id);
  const truncatedAddress = truncateAddress(account.address);

  return (
    <div className="w-full bg-gray-900 rounded-lg p-3 flex items-center gap-3 border border-gray-700 hover:bg-gray-800 transition-colors cursor-pointer">
      <WalletIcon walletId={wallet.id} size="24" />
      <div className="flex flex-col min-w-0 flex-1">
        <div className="text-sm font-medium text-white truncate">
          {truncatedAddress}
        </div>
        <div className="text-xs text-gray-400">{walletName}</div>
      </div>
    </div>
  );
};

/**
 * Enhanced Wallet Connect Button Component
 * Uses Thirdweb v5 ConnectButton with built-in wallet management features
 * Includes Send, Receive, Buy, View Assets, and Manage Wallet functionality
 */
const WalletConnectButton = () => {
  // Handle connection events
  const handleConnect = () => {
    // toast({
    //   title: "Wallet connected",
    //   description: `Successfully connected with wallet`,
    // });
  };

  const handleDisconnect = () => {
    toast({
      title: "Wallet disconnected",
      description: "Your wallet has been disconnected",
    });
  };

  return (
    <ConnectButton
      client={client}
      wallets={wallets}
      chains={chains}
      theme="dark"
      onConnect={handleConnect}
      onDisconnect={handleDisconnect}
      connectButton={{
        label: "Connect Wallet",
        className: "w-full nebula-action-button text-foreground border-none",
      }}
      connectModal={{
        title: "Connect to Nebula",
        size: "compact",
      }}
      detailsButton={{
        className: "w-full",
        render: () => <CustomWalletDetails />,
      }}
      detailsModal={{
        assetTabs: ["token", "nft"],
      }}
      supportedTokens={{
        [ethereum.id]: [
          {
            address: "******************************************", // Example USDC
            name: "USD Coin",
            symbol: "USDC",
            icon: "https://cryptologos.cc/logos/usd-coin-usdc-logo.png",
          },
        ],
        [polygon.id]: [
          {
            address: "******************************************", // USDC on Polygon
            name: "USD Coin",
            symbol: "USDC",
            icon: "https://cryptologos.cc/logos/usd-coin-usdc-logo.png",
          },
        ],
        [sepolia.id]: [
          {
            address: "******************************************", // Example test token
            name: "Test USDC",
            symbol: "USDC",
            icon: "https://cryptologos.cc/logos/usd-coin-usdc-logo.png",
          },
        ],
        [polygonAmoy.id]: [
          {
            address: "******************************************", // Example test token
            name: "Test USDC",
            symbol: "USDC",
            icon: "https://cryptologos.cc/logos/usd-coin-usdc-logo.png",
          },
        ],
      }}
    />
  );
};

export default WalletConnectButton;
